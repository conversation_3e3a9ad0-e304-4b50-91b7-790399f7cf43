# Modern C++23 CMake Template

A clean, modern C++23 project template with CMake, featuring automatic IDE integration, comprehensive testing setup, and best practices for C++ development.

## ✨ Features

-   **Modern C++23** standard with proper compiler feature detection
-   **CMakePresets.json** for standardized build configurations
-   **Automatic compile_commands.json** symlink for perfect IDE integration
-   **Catch2 testing framework** with automatic dependency management
-   **Clean project structure** with separate directories for source, headers, and tests
-   **Comprehensive compiler warnings** and cross-platform compatibility
-   **Multiple build types** (Debug, Release, RelWithDebInfo)
-   **Prevents in-source builds** for cleaner project management

## 📁 Project Structure

```
cmake_template/
├── CMakeLists.txt          # Main CMake configuration
├── CMakePresets.json       # Build presets for different configurations
├── compile_commands.json   # Auto-generated symlink for IDE integration
├── README.md              # This file
├── src/                   # Source files
│   ├── CMakeLists.txt     # Source-specific CMake configuration
│   └── main.cpp           # Main application entry point
├── include/               # Public header files (currently empty)
└── test/                  # Test files
    ├── CMakeLists.txt     # Test-specific CMake configuration
    └── test_main.cpp      # Test entry point (Catch2)
```

## 🔧 Requirements

-   **CMake 3.21** or higher
-   **C++23 compatible compiler**:
    -   GCC 11+
    -   Clang 14+
    -   MSVC 2022+
-   **Ninja build system** (recommended, but not required)

## 🚀 Quick Start

### 1. Clone and Customize

```bash
# Clone or copy this template
git clone <your-repo-url> my-project
cd my-project

# Customize the project name in CMakeLists.txt
# Change "MyProject" to your actual project name
```

### 2. Build with CMake Presets

```bash
# Configure for debug build
cmake --preset debug

# Build the project
cmake --build build/debug

# Run the executable
./build/debug/src/MyProject_app
```

### 3. Run Tests

```bash
# Build and run tests
cmake --build build/debug --target test

# Or run tests directly with CTest
cd build/debug && ctest --output-on-failure
```

## 🏗️ Build Configurations

This template provides several pre-configured build presets:

| Preset           | Description                                  | Use Case                           |
| ---------------- | -------------------------------------------- | ---------------------------------- |
| `debug`          | Debug build with symbols and no optimization | Development and debugging          |
| `release`        | Optimized release build                      | Production deployment              |
| `relwithdebinfo` | Optimized build with debug symbols           | Performance testing with debugging |

### Using Different Presets

```bash
# Debug build (default)
cmake --preset debug
cmake --build build/debug

# Release build
cmake --preset release
cmake --build build/release

# Release with debug info
cmake --preset relwithdebinfo
cmake --build build/relwithdebinfo
```

## 🧪 Testing

The template uses **Catch2 v3.8.1** for testing with automatic dependency management:

-   If Catch2 is installed system-wide, it will be used
-   If not found, Catch2 will be automatically downloaded via FetchContent
-   Tests are automatically discovered and registered with CTest

### Writing Tests

Add your test files to the `test/` directory and update `test/test_main.cpp`:

```cpp
#include <catch2/catch_test_macros.hpp>

TEST_CASE("Example test", "[example]")
{
    REQUIRE(1 + 1 == 2);
}

TEST_CASE("Another test with sections", "[example]")
{
    SECTION("Section 1")
    {
        REQUIRE(true);
    }

    SECTION("Section 2")
    {
        REQUIRE_FALSE(false);
    }
}
```

### Running Tests

```bash
# Run all tests
cmake --build build/debug --target test

# Run tests with verbose output
cd build/debug && ctest --verbose

# Run specific test executable directly
./build/debug/test/MyProject_tests

# Run tests with specific tags
./build/debug/test/MyProject_tests "[example]"
```

## 🎯 IDE Integration

This template provides excellent IDE support through automatic `compile_commands.json` generation:

### VS Code with clangd

1. **Install the clangd extension** (disable C/C++ extension if installed)
2. **Build the project once** to generate `compile_commands.json`:
    ```bash
    cmake --preset debug
    cmake --build build/debug
    ```
3. **The symlink is automatically created** in the project root for clangd to find
4. **Restart the language server** if needed: `Ctrl+Shift+P` → "clangd: Restart language server"

### CLion

1. **Open the project folder** in CLion
2. **CLion automatically detects** the CMake configuration
3. **Select your preferred preset** from the CMake profiles dropdown
4. **Build and run** directly from the IDE

### Other IDEs

Any IDE that supports `compile_commands.json` will work automatically:

-   **Qt Creator**: Automatically detects the compile commands
-   **Code::Blocks**: Import the CMake project
-   **Eclipse CDT**: Use the CMake4Eclipse plugin

## 🔧 Customization Guide

### 1. Rename Your Project

Edit the main `CMakeLists.txt` file:

```cmake
# Change these lines (around line 28):
project(
  YourProjectName              # <- Change this
  VERSION 1.0.0               # <- Update version
  DESCRIPTION "Your project description"  # <- Update description
  LANGUAGES CXX)
```

### 2. Add Source Files

Add new source files to `src/CMakeLists.txt`:

```cmake
# Update the executable definition:
add_executable(${PROJECT_NAME}_app
    main.cpp
    your_new_file.cpp           # Add your files here
    another_file.cpp
    # ... more files
)
```

### 3. Add Header Files

1. **Create your header structure** in the `include/` directory:

    ```
    include/
    └── yourproject/
        ├── core.h
        ├── utils.h
        └── algorithms/
            └── sorting.h
    ```

2. **Include headers** in your source files:
    ```cpp
    #include "yourproject/core.h"
    #include "yourproject/algorithms/sorting.h"
    ```

### 4. Add Dependencies

#### System Libraries

```cmake
# Add to CMakeLists.txt after the project() declaration:
find_package(YourLibrary REQUIRED)

# Link to your executable in src/CMakeLists.txt:
target_link_libraries(${PROJECT_NAME}_app
  PRIVATE
    ${PROJECT_NAME}::${PROJECT_NAME}_options
    ${PROJECT_NAME}::${PROJECT_NAME}_warnings
    YourLibrary::YourLibrary        # Add this line
)
```

#### Header-Only Libraries via FetchContent

```cmake
# Add to CMakeLists.txt:
include(FetchContent)
FetchContent_Declare(
  fmt
  GIT_REPOSITORY https://github.com/fmtlib/fmt.git
  GIT_TAG 10.1.1
)
FetchContent_MakeAvailable(fmt)

# Link in src/CMakeLists.txt:
target_link_libraries(${PROJECT_NAME}_app
  PRIVATE
    ${PROJECT_NAME}::${PROJECT_NAME}_options
    ${PROJECT_NAME}::${PROJECT_NAME}_warnings
    fmt::fmt                        # Add this line
)
```

## 📦 Advanced Usage

### Custom Build Types

You can add custom build types by modifying `CMakePresets.json`:

```json
{
    "name": "sanitize",
    "displayName": "Debug with Sanitizers",
    "inherits": "debug",
    "cacheVariables": {
        "CMAKE_CXX_FLAGS": "-fsanitize=address,undefined"
    }
}
```

### Cross-Platform Considerations

The template is designed to work across platforms:

-   **Windows**: Use Visual Studio 2022 or clang-cl
-   **Linux**: GCC 11+ or Clang 14+
-   **macOS**: Xcode 14+ or Homebrew Clang 14+

### Installation Support

To add installation support, add this to your main `CMakeLists.txt`:

```cmake
# Installation configuration
install(TARGETS ${PROJECT_NAME}_app
    RUNTIME DESTINATION bin
)

install(DIRECTORY include/
    DESTINATION include
    FILES_MATCHING PATTERN "*.h" PATTERN "*.hpp"
)
```

Then build and install:

```bash
cmake --preset release
cmake --build build/release
cmake --install build/release --prefix /usr/local
```

## 🔄 Continuous Integration

### GitHub Actions

Create `.github/workflows/ci.yml` for automated testing:

```yaml
name: CI

on:
    push:
        branches: [main, develop]
    pull_request:
        branches: [main]

jobs:
    build:
        strategy:
            matrix:
                os: [ubuntu-latest, windows-latest, macos-latest]
                build_type: [Debug, Release]

        runs-on: ${{ matrix.os }}

        steps:
            - uses: actions/checkout@v4

            - name: Install dependencies (Ubuntu)
              if: matrix.os == 'ubuntu-latest'
              run: |
                  sudo apt-get update
                  sudo apt-get install -y cmake ninja-build

            - name: Install dependencies (macOS)
              if: matrix.os == 'macos-latest'
              run: |
                  brew install cmake ninja

            - name: Configure CMake
              run: cmake --preset debug

            - name: Build
              run: cmake --build build/debug --config ${{ matrix.build_type }}

            - name: Test
              working-directory: build/debug
              run: ctest --output-on-failure --build-config ${{ matrix.build_type }}
```

### GitLab CI

Create `.gitlab-ci.yml`:

```yaml
stages:
    - build
    - test

variables:
    CMAKE_BUILD_TYPE: Debug

build:
    stage: build
    image: gcc:latest
    before_script:
        - apt-get update && apt-get install -y cmake ninja-build
    script:
        - cmake --preset debug
        - cmake --build build/debug
    artifacts:
        paths:
            - build/debug/
        expire_in: 1 hour

test:
    stage: test
    image: gcc:latest
    dependencies:
        - build
    script:
        - cd build/debug && ctest --output-on-failure
```

## 🐛 Troubleshooting

### Common Issues

#### 1. "compile_commands.json not found"

**Solution**: Build the project at least once to generate the file:

```bash
cmake --preset debug
cmake --build build/debug
```

#### 2. "CMake version too old"

**Error**: `CMake 3.21 or higher is required`

**Solution**: Update CMake:

```bash
# Ubuntu/Debian
sudo apt-get install cmake

# macOS
brew install cmake

# Or download from https://cmake.org/download/
```

#### 3. "C++23 features not available"

**Solution**: Ensure you have a compatible compiler:

-   **GCC**: Version 11 or higher
-   **Clang**: Version 14 or higher
-   **MSVC**: Visual Studio 2022 or higher

#### 4. "Ninja not found"

**Solution**: Install Ninja build system:

```bash
# Ubuntu/Debian
sudo apt-get install ninja-build

# macOS
brew install ninja

# Windows
# Download from https://github.com/ninja-build/ninja/releases
```

#### 5. Tests not running

**Solution**: Ensure tests are enabled:

```bash
# Check if BUILD_TESTING is ON
cmake --preset debug -DBUILD_TESTING=ON
cmake --build build/debug
```

### IDE-Specific Issues

#### VS Code clangd not working

1. **Disable C/C++ extension** if installed
2. **Install clangd extension**
3. **Ensure compile_commands.json exists** in project root
4. **Restart language server**: `Ctrl+Shift+P` → "clangd: Restart language server"

#### CLion not detecting CMake

1. **File → Open** (not Import)
2. **Select the project root directory**
3. **Wait for CMake to finish loading**
4. **Check CMake settings** in File → Settings → Build → CMake

## 📚 Additional Resources

### Documentation

-   [CMake Documentation](https://cmake.org/documentation/) - Official CMake documentation
-   [CMakePresets.json Reference](https://cmake.org/cmake/help/latest/manual/cmake-presets.7.html) - CMake presets specification
-   [Catch2 Documentation](https://github.com/catchorg/Catch2/tree/devel/docs) - Catch2 testing framework
-   [C++23 Reference](https://en.cppreference.com/w/cpp/23) - C++23 language features

### Best Practices

-   [Modern CMake](https://cliutils.gitlab.io/modern-cmake/) - Modern CMake practices
-   [C++ Core Guidelines](https://isocpp.github.io/CppCoreGuidelines/) - C++ best practices
-   [Effective Modern C++](https://www.oreilly.com/library/view/effective-modern-c/9781491908419/) - Modern C++ techniques

### Tools and Extensions

-   [clangd](https://clangd.llvm.org/) - C++ language server
-   [CMake Tools](https://marketplace.visualstudio.com/items?itemName=ms-vscode.cmake-tools) - VS Code CMake extension
-   [vcpkg](https://vcpkg.io/) - C++ package manager
-   [Conan](https://conan.io/) - C++ package manager

## 🤝 Contributing

When contributing to projects based on this template:

1. **Follow the existing code style** and project structure
2. **Add tests** for new functionality
3. **Update documentation** as needed
4. **Ensure all builds pass** before submitting PRs

## 📄 License

This template is provided as-is for educational and development purposes. Add your own license file as needed for your specific project.

---

**Happy coding with modern C++23! 🚀**

> **Note**: Remember to customize the project name, description, and other details in `CMakeLists.txt` before using this template for your own projects.
