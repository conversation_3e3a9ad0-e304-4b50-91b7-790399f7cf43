cmake_minimum_required(VERSION 3.21)

# Set the C++ standard
if (NOT DEFINED CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 23)
endif()

# Disable compiler extensions for better portability
set(CMAKE_CXX_EXTENSIONS OFF)

# Export compile commands for IDE integration
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# Create symlink to compile_commands.json in project root for clangd
if(CMAKE_EXPORT_COMPILE_COMMANDS)
  add_custom_target(
    symlink_compile_commands ALL
    COMMAND ${CMAKE_COMMAND} -E create_symlink
      ${CMAKE_BINARY_DIR}/compile_commands.json
      ${CMAKE_SOURCE_DIR}/compile_commands.json
    DEPENDS ${CMAKE_BINARY_DIR}/compile_commands.json
    COMMENT "Creating symlink to compile_commands.json in project root"
  )
endif()

# Set the project name and language
# TODO: Change 'MyProject' to your actual project name
project(
  MyProject
  VERSION 0.1.0
  DESCRIPTION "A modern C++23 project"
  LANGUAGES CXX)

# Prevent in-source builds
if(CMAKE_SOURCE_DIR STREQUAL CMAKE_BINARY_DIR)
  message(FATAL_ERROR "In-source builds are not allowed. Please create a separate build directory.")
endif()

# Set default build type to Release if not specified
if(NOT CMAKE_BUILD_TYPE AND NOT CMAKE_CONFIGURATION_TYPES)
  set(CMAKE_BUILD_TYPE "Release" CACHE STRING "Choose the type of build." FORCE)
  set_property(CACHE CMAKE_BUILD_TYPE PROPERTY STRINGS "Debug" "Release" "MinSizeRel" "RelWithDebInfo")
endif()

# Create interface libraries for project options and warnings
# TODO: These will be automatically renamed when you change the project name above
add_library(${PROJECT_NAME}_options INTERFACE)
add_library(${PROJECT_NAME}_warnings INTERFACE)

# Add compiler warnings
target_compile_options(${PROJECT_NAME}_warnings INTERFACE
  $<$<CXX_COMPILER_ID:MSVC>:/W4>
  $<$<NOT:$<CXX_COMPILER_ID:MSVC>>:-Wall -Wextra -Wpedantic>
)

# Set C++ standard for the options target
target_compile_features(${PROJECT_NAME}_options INTERFACE cxx_std_${CMAKE_CXX_STANDARD})

# Create aliases for easier usage
add_library(${PROJECT_NAME}::${PROJECT_NAME}_options ALIAS ${PROJECT_NAME}_options)
add_library(${PROJECT_NAME}::${PROJECT_NAME}_warnings ALIAS ${PROJECT_NAME}_warnings)

# Add include directory
target_include_directories(${PROJECT_NAME}_options INTERFACE
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>
)

# Add the src directory
add_subdirectory(src)

# Only build tests if this is the top-level project
if(PROJECT_IS_TOP_LEVEL)
  # Enable testing
  include(CTest)

  if(BUILD_TESTING)
    add_subdirectory(test)
  endif()
endif()
