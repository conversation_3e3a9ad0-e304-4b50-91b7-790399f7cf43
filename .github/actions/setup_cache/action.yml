
name: 'setup_cache'
description: 'sets up the shared cache'
inputs:
  compiler:
    required: true
    type: string
  build_type:
    required: true
    type: string
  generator:
    required: true
    type: string
  packaging_maintainer_mode:
    required: true
    type: string


runs:
  using: "composite"
  steps:
    - name: C<PERSON>
      uses: actions/cache@v3
      with:
        # You might want to add .ccache to your cache configuration?
        path: |
          ~/.cache/pip
          ~/.ccache
        key: ${{ runner.os }}-${{ inputs.compiler }}-${{ inputs.build_type }}-${{ inputs.generator }}-${{ inputs.packaging_maintainer_mode }}-${{ hashFiles('**/CMakeLists.txt') }}
        restore-keys: |
          ${{ runner.os }}-${{ inputs.compiler }}-${{ inputs.build_type }}

