name: ci
on:
  pull_request:
  release:
    types: [published]
  push:
    tags:
    branches:
      - main
      - develop

env:
  CLANG_TIDY_VERSION: "19.1.1"
  VERBOSE: 1


jobs:
  Test:
    name: ${{matrix.os}} ${{matrix.compiler}} ${{matrix.build_type}} ${{matrix.packaging_maintainer_mode == 'ON' && '(maintainer mode)' || ''}}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false

      # Recommendations:
      #   * support at least 2 operating systems
      #   * support at least 2 compilers
      #   * make sure all supported configurations for your project are built
      #
      # Disable/enable builds in this list to meet the above recommendations
      # and your own projects needs
      matrix:
        os:
          - ubuntu-latest
          - macos-latest
          - windows-latest
        compiler:
          # you can specify the version after `-` like "llvm-15.0.2".
          - llvm-19.1.1
          - gcc-14
        generator:
          - "Ninja Multi-Config"
        build_type:
          - Release
          - Debug
        packaging_maintainer_mode:
          - ON
          - OFF
        build_shared:
          - OFF

        exclude:
          # mingw is determined by this author to be too buggy to support
          - os: windows-latest
            compiler: gcc-14

        include:
          # Add appropriate variables for gcov version required. This will intentionally break
          # if you try to use a compiler that does not have gcov set
          - compiler: gcc-14
            gcov_executable: gcov-14
            enable_ipo: On

          - compiler: llvm-19.1.1
            enable_ipo: Off
            gcov_executable: "llvm-cov gcov"

          - os: macos-latest
            enable_ipo: Off

          # Set up preferred package generators, for given build configurations
          - build_type: Release
            packaging_maintainer_mode: On
            package_generator: TBZ2

          # This exists solely to make sure a non-multiconfig build works
          - os: ubuntu-latest
            compiler: gcc-14
            generator: "Unix Makefiles"
            build_type: Debug
            gcov_executable: gcov-14
            packaging_maintainer_mode: On
            enable_ipo: Off

          # Windows msvc builds
          - os: windows-latest
            compiler: msvc
            generator: "Visual Studio 17 2022"
            build_type: Debug
            packaging_maintainer_mode: On
            enable_ipo: On

          - os: windows-latest
            compiler: msvc
            generator: "Visual Studio 17 2022"
            build_type: Release
            packaging_maintainer_mode: On
            enable_ipo: On

          - os: windows-latest
            compiler: msvc
            generator: "Visual Studio 17 2022"
            build_type: Debug
            packaging_maintainer_mode: Off

          - os: windows-latest
            compiler: msvc
            generator: "Visual Studio 17 2022"
            build_type: Release
            packaging_maintainer_mode: Off
            package_generator: ZIP

          - os: windows-latest
            compiler: msvc
            generator: "Visual Studio 17 2022"
            build_type: Release
            packaging_maintainer_mode: On
            enable_ipo: On
            build_shared: On


    steps:
      - name: Check for llvm version mismatches
        if: ${{ contains(matrix.compiler, 'llvm') && !contains(matrix.compiler, env.CLANG_TIDY_VERSION) }}
        uses: actions/github-script@v7
        with:
          script: |
            core.setFailed('There is a mismatch between configured llvm compiler and clang-tidy version chosen')

      - uses: actions/checkout@v3

      - name: Setup Cache
        uses: ./.github/actions/setup_cache
        with:
          compiler: ${{ matrix.compiler }}
          build_type: ${{ matrix.build_type }}
          packaging_maintainer_mode: ${{ matrix.packaging_maintainer_mode }}
          generator: ${{ matrix.generator }}

      - name: Project Name
        uses: cardinalby/export-env-action@v2
        with:
          envFile: '.github/constants.env'


      - name: Setup Cpp
        uses: aminya/setup-cpp@v1
        with:
          compiler: ${{ matrix.compiler }}
          vcvarsall: ${{ contains(matrix.os, 'windows' )}}

          cmake: true
          ninja: true
          vcpkg: false
          ccache: true
          clangtidy: ${{ env.CLANG_TIDY_VERSION }}


          cppcheck: true

          gcovr: true
          opencppcoverage: true

      - name: Configure CMake
        run: |
          cmake -S . -B ./build -G "${{matrix.generator}}" -D${{ env.PROJECT_NAME }}_ENABLE_IPO=${{matrix.enable_ipo }} -DCMAKE_BUILD_TYPE:STRING=${{matrix.build_type}} -D${{ env.PROJECT_NAME }}_PACKAGING_MAINTAINER_MODE:BOOL=${{matrix.packaging_maintainer_mode}} -D${{ env.PROJECT_NAME }}_ENABLE_COVERAGE:BOOL=${{ matrix.build_type == 'Debug' }} -DGIT_SHA:STRING=${{ github.sha }}

      - name: Build
        # Execute the build.  You can specify a specific target with "--target <NAME>"
        run: |
          cmake --build ./build --config ${{matrix.build_type}}

      - name: Unix - Test and coverage
        if: runner.os != 'Windows'
        working-directory: ./build
        # Execute tests defined by the CMake configuration.
        # See https://cmake.org/cmake/help/latest/manual/ctest.1.html for more detail
        run: |
          ctest -C ${{matrix.build_type}}
          gcovr -j ${{env.nproc}} --root ../ --print-summary --xml-pretty --xml coverage.xml . --gcov-executable '${{ matrix.gcov_executable }}'

      - name: Windows - Test and coverage
        if: runner.os == 'Windows'
        working-directory: ./build
        run: |
          OpenCppCoverage.exe --export_type cobertura:coverage.xml --cover_children -- ctest -C ${{matrix.build_type}}

      - name: CPack
        if: matrix.package_generator != ''
        working-directory: ./build
        run: |
          cpack -C ${{matrix.build_type}} -G ${{matrix.package_generator}}

      - name: Publish Tagged Release
        uses: softprops/action-gh-release@v2
        if: ${{ startsWith(github.ref, 'refs/tags/') && matrix.package_generator != '' }}
        with:
          files: |
            build/*-*${{ matrix.build_type }}*-*.*


      - name: Publish to codecov
        uses: codecov/codecov-action@v5
        with:
          fail_ci_if_error: true # we weren't posting previously
          flags: ${{ runner.os }}
          name: ${{ runner.os }}-coverage
          token: ${{ secrets.CODECOV_TOKEN }}
          files: ./build/coverage.xml
