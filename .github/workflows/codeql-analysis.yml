# For most projects, this workflow file will not need changing; you simply need
# to commit it to your repository.
#
# You may wish to alter this file to override the set of languages analyzed,
# or to provide custom queries or build logic.
#
# ******** NOTE ********
# We have attempted to detect the languages in your repository. Please check
# the `language` matrix defined below to confirm you have the correct set of
# supported CodeQL languages.
#
name: "CodeQL"

on:
  push:
    branches: [ main, develop ]
  pull_request:
    # The branches below must be a subset of the branches above
    branches: [ main, develop ]
  schedule:
    - cron: '38 0 * * 5'


jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write

    strategy:
      fail-fast: false
      matrix:
        language: [ 'cpp' ]
        # CodeQL supports [ 'cpp', 'csharp', 'go', 'java', 'javascript', 'python', 'ruby' ]
        # Learn more about CodeQL language support at https://git.io/codeql-language-support
        compiler:
          # you can specify the version after `-` like "llvm-13.0.0".
          - gcc-14
        generator:
          - "Ninja Multi-Config"
        build_type:
          - Debug
        packaging_maintainer_mode:
          - ON


    steps:
    - uses: actions/checkout@v3

    - name: Setup Cache
      uses: ./.github/actions/setup_cache
      with:
        compiler: ${{ matrix.compiler }}
        build_type: ${{ matrix.build_type }}
        packaging_maintainer_mode: ${{ matrix.packaging_maintainer_mode }}
        generator: ${{ matrix.generator }}

    - name: Project Name
      uses: cardinalby/export-env-action@v2
      with:
        envFile: '.github/constants.env'


    - name: Setup Cpp
      uses: aminya/setup-cpp@v1
      with:
        compiler: ${{ matrix.compiler }}
        vcvarsall: ${{ contains(matrix.os, 'windows' )}}

        cmake: true
        ninja: true
        vcpkg: false
        ccache: true
        clangtidy: false

        cppcheck: false

        gcovr: false
        opencppcoverage: false

      # make sure coverage is only enabled for Debug builds, since it sets -O0 to make sure coverage
      # has meaningful results
    - name: Configure CMake
      run: |
        cmake -S . -B ./build -G "${{matrix.generator}}" -DCMAKE_BUILD_TYPE:STRING=${{matrix.build_type}} -D${{ env.PROJECT_NAME }}_PACKAGING_MAINTAINER_MODE:BOOL=${{matrix.packaging_maintainer_mode}} -D${{ env.PROJECT_NAME }}_ENABLE_COVERAGE:BOOL=${{ matrix.build_type == 'Debug' }}

    # Initializes the CodeQL tools for scanning.
    - name: Initialize CodeQL
      uses: github/codeql-action/init@v2
      with:
        languages: ${{ matrix.language }}
        # If you wish to specify custom queries, you can do so here or in a config file.
        # By default, queries listed here will override any specified in a config file.
        # Prefix the list here with "+" to use these queries and those in the config file.
        # queries: ./path/to/local/query, your-org/your-repo/queries@main


    - name: Build
      # Execute the build.  You can specify a specific target with "--target <NAME>"
      run: |
        cmake --build ./build --config ${{matrix.build_type}}

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v2
