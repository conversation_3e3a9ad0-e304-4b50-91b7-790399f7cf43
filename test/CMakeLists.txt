# Find Catch2 package
find_package(Catch2 3 QUIET)

if(NOT Catch2_FOUND)
  # If Catch2 is not found, try to fetch it
  include(FetchContent)
  FetchContent_Declare(
    Catch2
    GIT_REPOSITORY https://github.com/catchorg/Catch2.git
    GIT_TAG        v3.8.1
  )
  FetchContent_MakeAvailable(Catch2)
endif()

# Create test executable
# TODO: Change 'tests' to your preferred test executable name
add_executable(${PROJECT_NAME}_tests test_main.cpp)

# Link libraries
target_link_libraries(${PROJECT_NAME}_tests
  PRIVATE
    ${PROJECT_NAME}::${PROJECT_NAME}_options
    ${PROJECT_NAME}::${PROJECT_NAME}_warnings
    Catch2::Catch2WithMain
)

# Include Catch2 extras for test discovery
if(TARGET Catch2::Catch2)
  include(${Catch2_SOURCE_DIR}/extras/Catch.cmake)
  catch_discover_tests(${PROJECT_NAME}_tests)
endif()
