# Build directories and binary files
build/
out/
out/coverage/*
cmake-build-*/
conan-cache/

# Generated compilation database symlink
compile_commands.json

# User spesific settings
CMakeUserPresets.json

# IDE files
.vs/
.idea/
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.bak
*.swp
*~
_ReSharper*
*.log

# OS Generated Files
.DS_Store
.AppleDouble
.LSOverride
._*
.Spotlight-V100
.Trashes
.Trash-*
$RECYCLE.BIN/
.TemporaryItems
ehthumbs.db
Thumbs.db
